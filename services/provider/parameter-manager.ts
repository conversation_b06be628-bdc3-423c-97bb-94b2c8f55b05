/**
 * 统一的模型参数配置管理
 */

import { ModelParameterConfig } from './types';

// 导入各个provider的配置
import { ALL_GRSAI_PARAMETER_CONFIGS } from './grsai/parameters';
import { ALL_REPLICATE_PARAMETER_CONFIGS } from './replicate/parameters';

/**
 * 所有模型参数配置的集合
 */
const ALL_MODEL_CONFIGS: ModelParameterConfig[] = [
  ...ALL_GRSAI_PARAMETER_CONFIGS,
  ...ALL_REPLICATE_PARAMETER_CONFIGS
];

/**
 * 根据模型ID获取参数配置
 */
export function getModelParameterConfig(modelId: string): ModelParameterConfig | null {
  return ALL_MODEL_CONFIGS.find(config => config.modelId === modelId) || null;
}

/**
 * 根据provider获取所有模型配置
 */
export function getModelConfigsByProvider(provider: string): ModelParameterConfig[] {
  return ALL_MODEL_CONFIGS.filter(config => config.provider === provider);
}

/**
 * 根据模型类型获取所有模型配置
 */
export function getModelConfigsByType(modelType: string): ModelParameterConfig[] {
  return ALL_MODEL_CONFIGS.filter(config => config.modelType === modelType);
}

/**
 * 获取所有支持的模型ID列表
 */
export function getAllSupportedModelIds(): string[] {
  return ALL_MODEL_CONFIGS.map(config => config.modelId);
}

/**
 * 检查模型是否有参数配置
 */
export function hasParameterConfig(modelId: string): boolean {
  return ALL_MODEL_CONFIGS.some(config => config.modelId === modelId);
}

/**
 * 获取模型的默认参数值
 */
export function getModelDefaultParameters(modelId: string): Record<string, any> {
  const config = getModelParameterConfig(modelId);
  if (!config) return {};

  const defaults: Record<string, any> = {};
  config.parameters.forEach(param => {
    if (param.default !== undefined) {
      defaults[param.name] = param.default;
    }
  });

  return defaults;
}

/**
 * 验证参数值是否符合配置要求
 */
export function validateParameterValue(
  modelId: string, 
  paramName: string, 
  value: any
): { valid: boolean; error?: string } {
  const config = getModelParameterConfig(modelId);
  if (!config) {
    return { valid: false, error: '模型配置不存在' };
  }

  const param = config.parameters.find(p => p.name === paramName);
  if (!param) {
    return { valid: false, error: '参数不存在' };
  }

  // 必填检查
  if (param.required && (value === undefined || value === null || value === '')) {
    return { valid: false, error: `${param.description}是必填项` };
  }

  // 如果是非必填参数且值为undefined，跳过验证（使用默认值）
  if (!param.required && (value === undefined || value === null || value === '')) {
    return { valid: true };
  }

  // 类型检查
  switch (param.type) {
    case 'number':
      if (typeof value !== 'number' || isNaN(value)) {
        return { valid: false, error: `${param.description}必须是数字` };
      }
      if (param.min !== undefined && value < param.min) {
        return { valid: false, error: `${param.description}不能小于${param.min}` };
      }
      if (param.max !== undefined && value > param.max) {
        return { valid: false, error: `${param.description}不能大于${param.max}` };
      }
      break;

    case 'select':
      if (param.options && !param.options.some(opt => opt.value === value)) {
        return { valid: false, error: `${param.description}选项无效` };
      }
      break;

    case 'boolean':
      if (typeof value !== 'boolean') {
        return { valid: false, error: `${param.description}必须是布尔值` };
      }
      break;
  }

  return { valid: true };
}
