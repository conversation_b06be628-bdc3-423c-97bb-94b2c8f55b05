/**
 * GRSAI 文本模型参数配置
 */

import { ModelParameterConfig } from '../../types';

export const GRSAI_TEXT_MODELS: ModelParameterConfig[] = [
  {
    modelId: 'gemini-2.5-pro',
    version: '1.0',
    provider: 'grsai',
    modelType: 'text',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 8192,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量，影响回答长度',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性，0为最保守，1为最创新',
        group: 'basic'
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        description: '核心采样',
        tooltip: '控制词汇选择范围，较小值使输出更集中',
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        description: '流式输出',
        tooltip: '启用后将实时显示生成过程',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['top_p', 'stream'],
      expert: []
    }
  },
  {
    modelId: 'gemini-2.5-flash',
    version: '1.0',
    provider: 'grsai',
    modelType: 'text',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性',
        group: 'basic'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        description: '流式输出',
        tooltip: '启用后将实时显示生成过程',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['stream'],
      expert: []
    }
  },
  {
    modelId: 'gemini-2.5-flash-lite',
    version: '1.0',
    provider: 'grsai',
    modelType: 'text',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 500,
        min: 1,
        max: 2048,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性',
        group: 'basic'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: [],
      expert: []
    }
  },
  {
    modelId: 'gpt-4o-mini',
    version: '1.0',
    provider: 'grsai',
    modelType: 'text',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性',
        group: 'basic'
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        description: '核心采样',
        tooltip: '控制词汇选择范围',
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        description: '流式输出',
        tooltip: '启用后将实时显示生成过程',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['top_p', 'stream'],
      expert: []
    }
  }
];
