/**
 * GRSAI 多模态模型参数配置
 */

import { ModelParameterConfig } from '../../types';

export const GRSAI_MULTIMODAL_MODELS: ModelParameterConfig[] = [
  {
    modelId: 'gpt-4o-all',
    version: '1.0',
    provider: 'grsai',
    modelType: 'multimodal',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性，0为最保守，1为最创新',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '图片输入',
        tooltip: '上传图片进行视觉分析和理解',
        group: 'basic'
      },
      {
        name: 'vision_detail',
        type: 'select',
        required: false,
        default: 'auto',
        options: [
          { value: 'auto', label: '自动', description: '自动选择最佳图像处理方式' },
          { value: 'low', label: '低细节', description: '快速处理，适合简单图像' },
          { value: 'high', label: '高细节', description: '详细分析，适合复杂图像' }
        ],
        description: '图像分析细节',
        tooltip: '控制图像分析的详细程度',
        group: 'advanced',
        condition: {
          field: 'uploadedImages',
          value: [],
          operator: 'not_empty'
        }
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        description: '核心采样',
        tooltip: '控制词汇选择范围，较小值使输出更集中',
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        description: '流式输出',
        tooltip: '启用后将实时显示生成过程',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature', 'uploadedImages'],
      advanced: ['vision_detail', 'top_p', 'stream'],
      expert: []
    }
  },
  {
    modelId: 'o4-mini-all',
    version: '1.0',
    provider: 'grsai',
    modelType: 'multimodal',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 2048,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '图片输入',
        tooltip: '上传图片进行视觉分析和理解',
        group: 'basic'
      },
      {
        name: 'vision_detail',
        type: 'select',
        required: false,
        default: 'auto',
        options: [
          { value: 'auto', label: '自动', description: '自动选择最佳图像处理方式' },
          { value: 'low', label: '低细节', description: '快速处理，适合简单图像' },
          { value: 'high', label: '高细节', description: '详细分析，适合复杂图像' }
        ],
        description: '图像分析细节',
        tooltip: '控制图像分析的详细程度',
        group: 'advanced',
        condition: {
          field: 'uploadedImages',
          value: [],
          operator: 'not_empty'
        }
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        description: '流式输出',
        tooltip: '启用后将实时显示生成过程',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature', 'uploadedImages'],
      advanced: ['vision_detail', 'stream'],
      expert: []
    }
  }
];
