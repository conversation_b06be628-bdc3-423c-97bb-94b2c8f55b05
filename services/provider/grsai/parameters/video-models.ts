/**
 * GRSAI 视频模型参数配置
 */

import { ModelParameterConfig } from '../../types';

export const GRSAI_VIDEO_MODELS: ModelParameterConfig[] = [
  {
    modelId: 'veo3-fast',
    version: '1.0',
    provider: 'grsai',
    modelType: 'video',
    parameters: [
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '首帧图片',
        tooltip: '上传首帧图片作为视频的起始画面',
        group: 'basic'
      },
      {
        name: 'duration',
        type: 'select',
        required: false,
        default: '5',
        options: [
          { value: '5', label: '5秒', description: '短视频，快速生成' },
          { value: '10', label: '10秒', description: '中等长度视频' }
        ],
        description: '视频时长',
        tooltip: '选择生成视频的时长',
        group: 'basic'
      },
      {
        name: 'resolution',
        type: 'select',
        required: false,
        default: '720p',
        options: [
          { value: '720p', label: '720p (HD)', description: '高清分辨率，平衡质量和速度' },
          { value: '1080p', label: '1080p (Full HD)', description: '全高清分辨率，更高质量' }
        ],
        description: '视频分辨率',
        tooltip: '选择生成视频的分辨率',
        group: 'basic'
      },
      {
        name: 'fps',
        type: 'select',
        required: false,
        default: '24',
        options: [
          { value: '24', label: '24 FPS', description: '电影级帧率' },
          { value: '30', label: '30 FPS', description: '标准视频帧率' }
        ],
        description: '帧率',
        tooltip: '选择视频的帧率',
        group: 'advanced'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        tooltip: '选择视频存储和访问的CDN节点',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['uploadedImages', 'duration', 'resolution'],
      advanced: ['fps', 'cdn'],
      expert: []
    }
  },
  {
    modelId: 'veo3-pro',
    version: '1.0',
    provider: 'grsai',
    modelType: 'video',
    parameters: [
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '首帧图片',
        tooltip: '上传首帧图片作为视频的起始画面',
        group: 'basic'
      },
      {
        name: 'duration',
        type: 'select',
        required: false,
        default: '5',
        options: [
          { value: '5', label: '5秒', description: '短视频，快速生成' },
          { value: '10', label: '10秒', description: '中等长度视频' },
          { value: '15', label: '15秒', description: '长视频，更丰富内容' }
        ],
        description: '视频时长',
        tooltip: '选择生成视频的时长',
        group: 'basic'
      },
      {
        name: 'resolution',
        type: 'select',
        required: false,
        default: '1080p',
        options: [
          { value: '720p', label: '720p (HD)', description: '高清分辨率，快速生成' },
          { value: '1080p', label: '1080p (Full HD)', description: '全高清分辨率，标准质量' },
          { value: '4k', label: '4K (Ultra HD)', description: '超高清分辨率，最高质量' }
        ],
        description: '视频分辨率',
        tooltip: '选择生成视频的分辨率',
        group: 'basic'
      },
      {
        name: 'fps',
        type: 'select',
        required: false,
        default: '30',
        options: [
          { value: '24', label: '24 FPS', description: '电影级帧率' },
          { value: '30', label: '30 FPS', description: '标准视频帧率' },
          { value: '60', label: '60 FPS', description: '高帧率，更流畅' }
        ],
        description: '帧率',
        tooltip: '选择视频的帧率',
        group: 'advanced'
      },
      {
        name: 'quality',
        type: 'select',
        required: false,
        default: 'high',
        options: [
          { value: 'standard', label: '标准质量', description: '平衡质量和速度' },
          { value: 'high', label: '高质量', description: '更好的视觉效果' },
          { value: 'ultra', label: '超高质量', description: '最佳视觉效果，生成较慢' }
        ],
        description: '生成质量',
        tooltip: '选择视频生成的质量级别',
        group: 'advanced'
      },
      {
        name: 'motion_intensity',
        type: 'number',
        required: false,
        default: 5,
        min: 1,
        max: 10,
        description: '运动强度',
        tooltip: '控制视频中的运动幅度，1为静态，10为剧烈运动',
        group: 'expert'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        tooltip: '选择视频存储和访问的CDN节点',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['uploadedImages', 'duration', 'resolution'],
      advanced: ['fps', 'quality', 'cdn'],
      expert: ['motion_intensity']
    }
  }
];
