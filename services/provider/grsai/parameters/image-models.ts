/**
 * GRSAI 图像模型参数配置
 */

import { ModelParameterConfig, ASPECT_RATIO_OPTIONS, VARIANTS_OPTIONS } from '../../types';

export const GRSAI_IMAGE_MODELS: ModelParameterConfig[] = [
  {
    modelId: 'flux-kontext-max',
    version: '1.0',
    provider: 'grsai',
    modelType: 'image',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        description: '图片宽高比',
        tooltip: '选择生成图片的宽高比例',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '参考图片',
        tooltip: '上传参考图片，支持多张图片',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        tooltip: '选择图片存储和访问的CDN节点',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },
  {
    modelId: 'flux-kontext-pro',
    version: '1.0',
    provider: 'grsai',
    modelType: 'image',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        description: '图片宽高比',
        tooltip: '选择生成图片的宽高比例',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '参考图片',
        tooltip: '上传参考图片，支持多张图片',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        tooltip: '选择图片存储和访问的CDN节点',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },
  {
    modelId: 'flux-pro-1.1',
    version: '1.0',
    provider: 'grsai',
    modelType: 'image',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        description: '图片宽高比',
        tooltip: '选择生成图片的宽高比例',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio'],
      advanced: ['cdn'],
      expert: []
    }
  },
  {
    modelId: 'flux-pro-1.1-ultra',
    version: '1.0',
    provider: 'grsai',
    modelType: 'image',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        description: '图片宽高比',
        tooltip: '选择生成图片的宽高比例',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio'],
      advanced: ['cdn'],
      expert: []
    }
  },
  {
    modelId: 'gpt-4o-image',
    version: '1.0',
    provider: 'grsai',
    modelType: 'image',
    parameters: [
      {
        name: 'variants',
        type: 'number',
        required: false,
        default: 1,
        min: 1,
        max: 2,
        description: '生成数量',
        tooltip: '选择生成图片的数量（1-2张）',
        group: 'basic'
      },
      {
        name: 'size',
        type: 'select',
        required: false,
        default: '1024x1024',
        options: [
          { value: '1024x1024', label: '正方形 (1024x1024)', description: '标准正方形尺寸' },
          { value: '1792x1024', label: '横屏 (1792x1024)', description: '宽屏尺寸' },
          { value: '1024x1792', label: '竖屏 (1024x1792)', description: '竖屏尺寸' }
        ],
        description: '图片尺寸',
        tooltip: '选择生成图片的具体尺寸',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '参考图片',
        tooltip: '上传参考图片，支持多张图片',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['variants', 'size', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },
  {
    modelId: 'sora-image',
    version: '1.0',
    provider: 'grsai',
    modelType: 'image',
    parameters: [
      {
        name: 'variants',
        type: 'number',
        required: false,
        default: 1,
        min: 1,
        max: 2,
        description: '生成数量',
        tooltip: '选择生成图片的数量（1-2张）',
        group: 'basic'
      },
      {
        name: 'size',
        type: 'select',
        required: false,
        default: '1024x1024',
        options: [
          { value: '1024x1024', label: '正方形 (1024x1024)', description: '标准正方形尺寸' },
          { value: '1792x1024', label: '横屏 (1792x1024)', description: '宽屏尺寸' },
          { value: '1024x1792', label: '竖屏 (1024x1792)', description: '竖屏尺寸' }
        ],
        description: '图片尺寸',
        tooltip: '选择生成图片的具体尺寸',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '参考图片',
        tooltip: '上传参考图片，支持多张图片',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['variants', 'size', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  }
];
