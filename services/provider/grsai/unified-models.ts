/**
 * GRSAI 统一模型配置
 * 包含模型基础信息和参数配置
 */

import { ParameterConfig, ASPECT_RATIO_OPTIONS, VARIANTS_OPTIONS } from '../types';

export enum ModelType {
  TEXT = 'text',
  IMAGE = 'image', 
  VIDEO = 'video',
  MULTIMODAL = 'multimodal'
}

export enum Provider {
  GRSAI = 'grsai',
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic'
}

export enum UnitType {
  TOKENS = 'tokens',
  IMAGES = 'images', 
  VIDEOS = 'videos'
}

export interface UnifiedModelConfig {
  // 基础模型信息
  id: string;
  name: string;
  type: ModelType;
  provider: Provider;
  apiEndpoint: string;
  creditsPerUnit: number;
  unitType: UnitType;
  isActive: boolean;
  description?: string;
  maxInputSize?: number;
  supportedFeatures?: string[];
  icon?: string;
  
  // 参数配置
  parameters: ParameterConfig[];
  parameterGroups: {
    basic: string[];
    advanced: string[];
    expert: string[];
  };
}

/**
 * GRSAI 统一模型配置
 */
export const GRSAI_UNIFIED_MODELS: UnifiedModelConfig[] = [
  // 文本生成模型
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 10,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: '高级对话模型，适合复杂任务和专业用途',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'conversation', 'analysis'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 8192,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量，影响回答长度',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性，0为最保守，1为最创新',
        group: 'basic'
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        description: '核心采样',
        tooltip: '控制词汇选择范围，较小值使输出更集中',
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        description: '流式输出',
        tooltip: '启用后将实时显示生成过程',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['top_p', 'stream'],
      expert: []
    }
  },
  
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 5,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: '快速对话模型，响应迅速，效率高',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'conversation', 'fast_response'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性',
        group: 'basic'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        description: '流式输出',
        tooltip: '启用后将实时显示生成过程',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['stream'],
      expert: []
    }
  },
  
  {
    id: 'gemini-2.5-flash-lite',
    name: 'Gemini 2.5 Flash Lite',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 2,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: '轻量级对话模型，成本低廉，功能基础',
    maxInputSize: 64000,
    supportedFeatures: ['text_generation', 'basic_conversation'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 500,
        min: 1,
        max: 2048,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性',
        group: 'basic'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: [],
      expert: []
    }
  },
  
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 8,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: 'GPT-4o 轻量版本，性能与成本平衡',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'conversation', 'reasoning'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性',
        group: 'basic'
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        description: '核心采样',
        tooltip: '控制词汇选择范围',
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        description: '流式输出',
        tooltip: '启用后将实时显示生成过程',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['top_p', 'stream'],
      expert: []
    }
  },

  // 多模态模型
  {
    id: 'o4-mini-all',
    name: 'GPT-4o Mini All',
    type: ModelType.MULTIMODAL,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 12,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: 'GPT-4o Mini 全功能版本，支持视觉等多模态能力',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'vision', 'multimodal', 'reasoning'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 2048,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '图片输入',
        tooltip: '上传图片进行视觉分析和理解',
        group: 'basic'
      },
      {
        name: 'vision_detail',
        type: 'select',
        required: false,
        default: 'auto',
        options: [
          { value: 'auto', label: '自动', description: '自动选择最佳图像处理方式' },
          { value: 'low', label: '低细节', description: '快速处理，适合简单图像' },
          { value: 'high', label: '高细节', description: '详细分析，适合复杂图像' }
        ],
        description: '图像分析细节',
        tooltip: '控制图像分析的详细程度',
        group: 'advanced',
        condition: {
          field: 'uploadedImages',
          value: [],
          operator: 'not_empty'
        }
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        description: '流式输出',
        tooltip: '启用后将实时显示生成过程',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature', 'uploadedImages'],
      advanced: ['vision_detail', 'stream'],
      expert: []
    }
  },

  {
    id: 'gpt-4o-all',
    name: 'GPT-4o All',
    type: ModelType.MULTIMODAL,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 20,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: 'GPT-4o 完整版本，具备所有高级功能和多模态支持',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'vision', 'multimodal', 'advanced_reasoning', 'code_generation'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性，0为最保守，1为最创新',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '图片输入',
        tooltip: '上传图片进行视觉分析和理解',
        group: 'basic'
      },
      {
        name: 'vision_detail',
        type: 'select',
        required: false,
        default: 'auto',
        options: [
          { value: 'auto', label: '自动', description: '自动选择最佳图像处理方式' },
          { value: 'low', label: '低细节', description: '快速处理，适合简单图像' },
          { value: 'high', label: '高细节', description: '详细分析，适合复杂图像' }
        ],
        description: '图像分析细节',
        tooltip: '控制图像分析的详细程度',
        group: 'advanced',
        condition: {
          field: 'uploadedImages',
          value: [],
          operator: 'not_empty'
        }
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        description: '核心采样',
        tooltip: '控制词汇选择范围，较小值使输出更集中',
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        description: '流式输出',
        tooltip: '启用后将实时显示生成过程',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature', 'uploadedImages'],
      advanced: ['vision_detail', 'top_p', 'stream'],
      expert: []
    }
  }
];
