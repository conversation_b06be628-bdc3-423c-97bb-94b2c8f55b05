/**
 * 本地化AI模型数据
 * 从数据库迁移到本地存储，提升性能和可用性
 * GRSAI模型配置已迁移到 services/provider/grsai/models.ts
 */

import { GRSAI_MODELS, ModelConfig } from '@/services/provider/grsai/models';

export interface LocalAIModel {
  id: number;
  model_id: string;
  model_name: string;
  model_type: 'text' | 'image' | 'video' | 'multimodal';
  provider: string;
  api_endpoint: string;
  credits_per_unit: number;
  unit_type: string;
  is_active: boolean;
  description?: string;
  description_i18n?: Record<string, string>;
  model_name_i18n?: Record<string, string>;
  max_input_size?: number;
  supported_features?: string[];
  icon?: string;
  created_at: string;
  updated_at: string;
}

/**
 * 本地化AI模型数据 - 主要用于翻译内容和非GRSAI模型
 * GRSAI模型配置已迁移到 services/provider/grsai/models.ts
 */
export const LOCAL_AI_MODELS: LocalAIModel[] = [

  // Replicate 图像生成模型（保留翻译内容）
  {
    id: 15,
    model_id: "black-forest-labs/flux-krea-dev",
    model_name: "Flux Krea Dev",
    model_type: "image",
    provider: "replicate",
    api_endpoint: "/replicate/image",
    credits_per_unit: 25,
    unit_type: "images",
    is_active: true,
    description_i18n: {
      "en": "An opinionated text-to-image model from Black Forest Labs in collaboration with Krea that excels in photorealism. Creates images that avoid the oversaturated AI look.",
      "zh": "来自 Black Forest Labs 与 Krea 合作的专业文本转图像模型，擅长生成逼真的照片效果，避免过度饱和的 AI 风格。"
    },
    model_name_i18n: {
      "en": "Flux Krea Dev",
      "zh": "Flux Krea 开发版"
    },
    max_input_size: 2000,
    supported_features: ["text2image", "img2img", "photorealism", "high_quality", "image_upload"],
    icon: "/imgs/icons/flux.svg",
    created_at: "2025-07-31T17:06:11.205885Z",
    updated_at: "2025-07-31T17:06:11.205885Z"
  }
];

/**
 * 将 GRSAI ModelConfig 转换为 LocalAIModel 格式
 */
function convertGRSAIModelToLocal(model: ModelConfig, id: number): LocalAIModel {
  return {
    id,
    model_id: model.id,
    model_name: model.name,
    model_type: model.type as 'text' | 'image' | 'video' | 'multimodal',
    provider: model.provider.toLowerCase(),
    api_endpoint: model.apiEndpoint,
    credits_per_unit: model.creditsPerUnit,
    unit_type: model.unitType.toLowerCase(),
    is_active: model.isActive,
    description: model.description,
    max_input_size: model.maxInputSize,
    supported_features: model.supportedFeatures,
    icon: model.icon,
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  };
}

/**
 * 获取所有模型（包括 GRSAI 和本地模型）
 */
function getAllModels(): LocalAIModel[] {
  // 转换 GRSAI 模型
  const grsaiModels = GRSAI_MODELS.map((model, index) =>
    convertGRSAIModelToLocal(model, 100 + index)
  );

  // 合并所有模型
  return [...grsaiModels, ...LOCAL_AI_MODELS];
}

/**
 * 处理本地化内容的工具函数
 */
export function getLocalizedContent(
  i18nContent: Record<string, string> | undefined,
  locale: string,
  fallbackLocale: string = 'en'
): string {
  if (!i18nContent) return '';
  return i18nContent[locale] || i18nContent[fallbackLocale] || Object.values(i18nContent)[0] || '';
}

/**
 * 处理模型的本地化信息
 */
export function processLocalModelWithLocalization(model: LocalAIModel, locale: string = 'en'): LocalAIModel {
  return {
    ...model,
    model_name: getLocalizedContent(model.model_name_i18n, locale, 'en') || model.model_name,
    description: getLocalizedContent(model.description_i18n, locale, 'en') || model.description || ''
  };
}

/**
 * 获取所有活跃的AI模型（本地版本）
 */
export function getLocalActiveAIModels(locale: string = 'en'): LocalAIModel[] {
  return getAllModels()
    .filter(model => model.is_active)
    .map(model => processLocalModelWithLocalization(model, locale))
    .sort((a, b) => {
      // 先按模型类型排序，再按积分消耗排序
      if (a.model_type !== b.model_type) {
        const typeOrder = { text: 0, multimodal: 1, image: 2, video: 3 };
        return (typeOrder[a.model_type] || 999) - (typeOrder[b.model_type] || 999);
      }
      return a.credits_per_unit - b.credits_per_unit;
    });
}

/**
 * 根据模型类型获取模型列表（本地版本）
 */
export function getLocalAIModelsByType(type: string, locale: string = 'en'): LocalAIModel[] {
  return getAllModels()
    .filter(model => model.is_active && model.model_type === type)
    .map(model => processLocalModelWithLocalization(model, locale))
    .sort((a, b) => a.credits_per_unit - b.credits_per_unit);
}

/**
 * 根据模型ID获取单个模型（本地版本）
 */
export function getLocalAIModelById(modelId: string, locale: string = 'en'): LocalAIModel | null {
  const model = getAllModels().find(m => m.model_id === modelId && m.is_active);
  return model ? processLocalModelWithLocalization(model, locale) : null;
}

/**
 * 根据模型ID获取单个模型（包括不活跃的模型，本地版本）
 */
export function getLocalAIModelByIdIncludeInactive(modelId: string, locale: string = 'en'): LocalAIModel | null {
  const model = getAllModels().find(m => m.model_id === modelId);
  return model ? processLocalModelWithLocalization(model, locale) : null;
}

/**
 * 根据提供商获取模型列表（本地版本）
 */
export function getLocalAIModelsByProvider(provider: string, locale: string = 'en'): LocalAIModel[] {
  return getAllModels()
    .filter(model => model.is_active && model.provider === provider)
    .map(model => processLocalModelWithLocalization(model, locale))
    .sort((a, b) => {
      if (a.model_type !== b.model_type) {
        const typeOrder = { text: 0, multimodal: 1, image: 2, video: 3 };
        return (typeOrder[a.model_type] || 999) - (typeOrder[b.model_type] || 999);
      }
      return a.credits_per_unit - b.credits_per_unit;
    });
}
